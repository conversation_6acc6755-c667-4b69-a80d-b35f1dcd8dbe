using System;
using System.Drawing;
using System.Windows.Forms;
using OCRTools.Properties;

namespace OCRTools.WebBroswerEx
{
    /// <summary>
    /// 简单的加载指示器
    /// </summary>
    public class LoadingIndicator : IDisposable
    {
        private readonly PictureBox _indicator;
        private Control _parent;
        private bool _isDisposed = false;

        public LoadingIndicator()
        {
            _indicator = new PictureBox
            {
                AccessibleDefaultActionDescription = "webloading",
                SizeMode = PictureBoxSizeMode.CenterImage,
                Image = Resources.webloading,
                Visible = false,
                BackColor = Color.White,
                Dock = DockStyle.Fill
            };
        }

        /// <summary>
        /// 设置父容器
        /// </summary>
        public void SetParent(Control parent)
        {
            if (_isDisposed) return;

            _parent = parent;
            if (_parent != null && !_parent.Controls.Contains(_indicator))
            {
                _parent.Controls.Add(_indicator);
            }
        }

        /// <summary>
        /// 显示加载指示器
        /// </summary>
        public void Show()
        {
            if (_isDisposed || _parent == null) return;

            try
            {
                if (_parent.InvokeRequired)
                {
                    _parent.Invoke(new Action(() =>
                    {
                        _indicator.Visible = true;
                        _indicator.BringToFront();
                    }));
                }
                else
                {
                    _indicator.Visible = true;
                    _indicator.BringToFront();
                }
            }
            catch { }
        }

        /// <summary>
        /// 隐藏加载指示器
        /// </summary>
        public void Hide()
        {
            if (_isDisposed) return;

            try
            {
                if (_parent?.InvokeRequired == true)
                {
                    _parent.Invoke(new Action(() => _indicator.Visible = false));
                }
                else
                {
                    _indicator.Visible = false;
                }
            }
            catch { }
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                try
                {
                    Hide();
                    if (_parent != null && _parent.Controls.Contains(_indicator))
                    {
                        _parent.Controls.Remove(_indicator);
                    }
                    _indicator?.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"LoadingIndicator.Dispose error: {ex.Message}");
                }
                finally
                {
                    _isDisposed = true;
                }
            }
        }
    }
}
