﻿2025-07-29 09:01:38 718
日志类型：操作日志
详情：[60c81d6a] 09:01:37.724 SmartWebControl初始化开始
[60c81d6a] 09:01:37.725 开始WebView2可用性检查
[60c81d6a] 09:01:37.774 WebView2可用性检查完成 - 耗时: 49ms, 结果: True
[60c81d6a] 09:01:37.774 === WebView2检查详情（首次实例）===
[60c81d6a] 09:01:37.726 开始WebView2可用性检查
[60c81d6a] 09:01:37.726 创建STA线程进行初始化
[60c81d6a] 09:01:37.727 等待STA线程完成
[60c81d6a] 09:01:37.729 STA线程启动 - 线程ID: 29, 线程模型: STA
[60c81d6a] 09:01:37.729 开始WebView2组件初始化
[60c81d6a] 09:01:37.756 WebView2组件初始化完成 - 耗时: 27ms, 结果: True
[60c81d6a] 09:01:37.756 开始WebView2环境创建
[60c81d6a] 09:01:37.771 WebView2环境创建完成 - 耗时: 15ms, 结果: True
[60c81d6a] 09:01:37.774 STA线程执行完成 - 总耗时: 48ms
[60c81d6a] 1. WebView2组件初始化: True
[60c81d6a] 2. 环境创建: True
[60c81d6a] WebView2完全可用
[60c81d6a] 09:01:37.774 === WebView2检查结束 ===
[60c81d6a] 09:01:37.774 选择WebView2引擎
[60c81d6a] 09:01:37.774 开始异步初始化
[60c81d6a] 09:01:37.776 开始WebView2 (Chromium)引擎初始化
[60c81d6a] 09:01:38.378 WebView2 (Chromium)引擎初始化完成 - 耗时: 601ms, 结果: True
[60c81d6a] 09:01:38.379 WebView2验证通过
[60c81d6a] 09:01:38.383 初始化成功，最终使用: WebView2 (Chromium) - 总耗时: 606ms
----------------------------------------
2025-07-29 09:01:41 668
日志类型：操作日志
详情：[f30f3b16] 09:01:41.002 SmartWebControl初始化开始
[f30f3b16] 09:01:41.002 开始WebView2可用性检查
[f30f3b16] 09:01:41.002 WebView2可用性检查完成 - 耗时: 0ms, 结果: True
[f30f3b16] 09:01:41.002 WebView2检查详情: 使用缓存结果: True
[f30f3b16] 09:01:41.002 选择WebView2引擎
[f30f3b16] 09:01:41.002 开始异步初始化
[f30f3b16] 09:01:41.002 开始WebView2 (Chromium)引擎初始化
[f30f3b16] 09:01:41.329 WebView2 (Chromium)引擎初始化完成 - 耗时: 327ms, 结果: True
[f30f3b16] 09:01:41.329 WebView2验证通过
[f30f3b16] 09:01:41.334 初始化成功，最终使用: WebView2 (Chromium) - 总耗时: 332ms
----------------------------------------
2025-07-29 09:01:45 113
日志类型：操作日志
详情：[e32b5098] 09:01:44.731 SmartWebControl初始化开始
[e32b5098] 09:01:44.731 开始WebView2可用性检查
[e32b5098] 09:01:44.731 WebView2可用性检查完成 - 耗时: 0ms, 结果: True
[e32b5098] 09:01:44.731 WebView2检查详情: 使用缓存结果: True
[e32b5098] 09:01:44.731 选择WebView2引擎
[e32b5098] 09:01:44.731 开始异步初始化
[e32b5098] 09:01:44.731 开始WebView2 (Chromium)引擎初始化
[e32b5098] 09:01:44.776 WebView2 (Chromium)引擎初始化完成 - 耗时: 45ms, 结果: True
[e32b5098] 09:01:44.776 WebView2验证通过
[e32b5098] 09:01:44.779 初始化成功，最终使用: WebView2 (Chromium) - 总耗时: 48ms
----------------------------------------
2025-07-29 09:23:20 179
日志类型：操作日志
详情：[4cd20456] 09:23:19.121 SmartWebControl初始化开始
[4cd20456] 09:23:19.121 开始WebView2可用性检查
[4cd20456] 09:23:19.175 WebView2可用性检查完成 - 耗时: 54ms, 结果: True
[4cd20456] 09:23:19.176 === WebView2检查详情（首次实例）===
[4cd20456] 09:23:19.122 开始WebView2可用性检查
[4cd20456] 09:23:19.122 创建STA线程进行初始化
[4cd20456] 09:23:19.122 等待STA线程完成
[4cd20456] 09:23:19.125 STA线程启动 - 线程ID: 29, 线程模型: STA
[4cd20456] 09:23:19.125 开始WebView2组件初始化
[4cd20456] 09:23:19.147 WebView2组件初始化完成 - 耗时: 22ms, 结果: True
[4cd20456] 09:23:19.147 开始WebView2环境创建
[4cd20456] 09:23:19.172 WebView2环境创建完成 - 耗时: 25ms, 结果: True
[4cd20456] 09:23:19.175 STA线程执行完成 - 总耗时: 53ms
[4cd20456] 1. WebView2组件初始化: True
[4cd20456] 2. 环境创建: True
[4cd20456] WebView2完全可用
[4cd20456] 09:23:19.176 === WebView2检查结束 ===
[4cd20456] 09:23:19.176 选择WebView2引擎
[4cd20456] 09:23:19.176 开始异步初始化
[4cd20456] 09:23:19.177 开始WebView2 (Chromium)引擎初始化
[4cd20456] 09:23:19.806 WebView2 (Chromium)引擎初始化完成 - 耗时: 629ms, 结果: True
[4cd20456] 09:23:19.807 WebView2验证通过
[4cd20456] 09:23:19.813 初始化成功，最终使用: WebView2 (Chromium) - 总耗时: 636ms
----------------------------------------
2025-07-29 09:23:28 479
日志类型：操作日志
详情：[65608d10] 09:23:27.842 SmartWebControl初始化开始
[65608d10] 09:23:27.842 开始WebView2可用性检查
[65608d10] 09:23:27.842 WebView2可用性检查完成 - 耗时: 0ms, 结果: True
[65608d10] 09:23:27.842 WebView2检查详情: 使用缓存结果: True
[65608d10] 09:23:27.842 选择WebView2引擎
[65608d10] 09:23:27.842 开始异步初始化
[65608d10] 09:23:27.842 开始WebView2 (Chromium)引擎初始化
[65608d10] 09:23:28.110 WebView2 (Chromium)引擎初始化完成 - 耗时: 268ms, 结果: True
[65608d10] 09:23:28.111 WebView2验证通过
[65608d10] 09:23:28.113 初始化成功，最终使用: WebView2 (Chromium) - 总耗时: 271ms
----------------------------------------
2025-07-29 09:23:30 602
日志类型：操作日志
详情：[f7216786] 09:23:30.169 SmartWebControl初始化开始
[f7216786] 09:23:30.169 开始WebView2可用性检查
[f7216786] 09:23:30.169 WebView2可用性检查完成 - 耗时: 0ms, 结果: True
[f7216786] 09:23:30.169 WebView2检查详情: 使用缓存结果: True
[f7216786] 09:23:30.169 选择WebView2引擎
[f7216786] 09:23:30.169 开始异步初始化
[f7216786] 09:23:30.169 开始WebView2 (Chromium)引擎初始化
[f7216786] 09:23:30.232 WebView2 (Chromium)引擎初始化完成 - 耗时: 63ms, 结果: True
[f7216786] 09:23:30.232 WebView2验证通过
[f7216786] 09:23:30.236 初始化成功，最终使用: WebView2 (Chromium) - 总耗时: 67ms
----------------------------------------
